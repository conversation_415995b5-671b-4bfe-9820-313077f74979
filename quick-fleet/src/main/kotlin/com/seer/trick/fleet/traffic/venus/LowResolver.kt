package com.seer.trick.fleet.traffic.venus

import com.seer.trick.BzError
import com.seer.trick.fleet.FleetLogger
import com.seer.trick.fleet.domain.*
import com.seer.trick.fleet.map.AreaMapCache
import com.seer.trick.fleet.seer.RotationDirection
import com.seer.trick.fleet.service.MapService
import com.seer.trick.fleet.service.RobotLoadCollisionService
import com.seer.trick.fleet.service.SceneRuntime
import com.seer.trick.fleet.traffic.venus.cache.DijkstraCache
import com.seer.trick.helper.IdHelper
import java.util.*
import kotlin.math.ceil
import kotlin.math.PI

/**
 * 低层求解器。支持多目标。
 * 为方便重现，不要对外部配置有依赖。
 * 注意：尽量独立，不要访问 sr/rr
 */
class TargetManyLowResolver(
  private val sr: SceneRuntime, // TODO 去掉对 sr 的依赖
  private val robotName: String,
  private val request: RobotPlanRequest,
  private val areaMapCache: AreaMapCache,
  private val collisionModels: Map<String, RobotLoadCollisionModel>,
  private val reserveTargetPoint: String?, // 预留路径点，最多一个
  private val highResolverFriendlyId: String,
  private val highNodeId: Long,
  private val constraints: List<RobotConstraint>, // 机器人规划的约束
  private val robots: Map<String, RobotPlanContext>, // 所有机器人的，用于计算冲突
  private val o: PathFindingOption,
) {

  private val logModuleMapfLow = "LowRv2"

  val id = IdHelper.oidStr()

  private val goals: List<RoutePosition>

  private var initPathOk = true

  /**
   * TODO 注释
   */
  private var initPathStates: List<State>? = null

  /**
   * 每个求解单个目标是的初始状态。
   * 求解第一个目标时，机器人有多个可能的起点：估测机器人可能位于多个点位、路径上。
   * 后面目标，机器人的起点应该是确定的，都在点上。
   * TODO 是否要在进入底层前就把多个 startStates 决策好
   * TODO 有没有可能变为单个状态
   */
  private var startStates: List<State>? = null

  /**
   * 当前正在求解的目标
   */
  private var goalIndex = 0

  /**
   * 每个目标的解
   */
  private val solutions = mutableListOf<RobotSolution>()

  /**
   * TODO 这个为什么不 merge
   */
  private var timeNum = 0L

  private val startOn = System.currentTimeMillis()

  private val cat = ConflictAvoidanceTable()

  init {
    // 初始化 CAT
    for ((robot, ctx) in robots) {
      if (robot == robotName) continue
      val path = ctx.solution?.path ?: continue
      cat.addPath(path)
    }

    // 初始化目标序列
    val goals = mutableListOf<RoutePosition>()
    val tt = request.trafficTask
    if (tt != null) {
      goals += RoutePosition.from(tt.target)
    }
    // 预留路径点一定在目标点后
    if (!reserveTargetPoint.isNullOrBlank()) {
      val point = areaMapCache.pointNameMap[reserveTargetPoint]!!.point
      goals += RoutePosition(RoutePositionType.Point, pointName = point.name, x = point.x, y = point.y, reserved = true)
    }
    this.goals = goals
  }

  fun search(purpose: String): RobotSolution {
    FleetLogger.trace(
      module = logModuleMapfLow,
      subject = "LowSearchStart",
      sr = sr,
      robotName = robotName,
      msg = mapOf(
        "purpose" to purpose,
        "highNodeId" to highNodeId,
        "request" to request,
        "reserveTargetPoint" to reserveTargetPoint,
        // "robotConstraints" to robotConstraints,
        "goals" to goals,
      ),
    )

    if (isGoalsConflict()) {
      return RobotSolution(
        robotName,
        false,
        "目标点违反约束， 一定无解",
        planCost = System.currentTimeMillis() - startOn,
      )
    }

    // 检查和初始化路径
    checkInitPath()

    if (!initPathOk) {
      return RobotSolution(
        robotName,
        false,
        "初始路径中违反约束",
        planCost = System.currentTimeMillis() - startOn,
      )
    }

    if (goalIndex == goals.size) {
      // 初始路径已到达目标
      return mergeSolutions()
    }

    // 可能有多个起点
    var initStates: List<State>? = null

    // 此时 startStates 不为空的话，只有一种情况，initPath 已经到了第一个目标点终点
    // 由于要计算第二个目标，所以在 checkInitPath 中设置了 startStates
    if (startStates.isNullOrEmpty()) {
      when {
        // 若已有初始化路径且尚未设置 startStates，则直接使用
        // 已有初始路径，但初始路径还没到达第一个目标终点
        !initPathStates.isNullOrEmpty() -> initStates = initPathStates

        else -> {
          // 从机器人当前位置推断多个可能的起点
          val candidates = VenusHelper.robotStandToStartStates(robotName, areaMapCache, request, o)

          // 检查是否存在违反约束的起点，并保留违规信息

          var invalidConstraint: RobotConstraint? = null

          val invalidState = candidates.find { state ->
            val constraint = LowResolverCommon.isValidNeighbor(
              robotName,
              constraints,
              goals[0],
              null,
              state,
              o,
              areaMapCache,
            )
            if (constraint != null) {
              invalidConstraint = constraint
              true
            } else {
              false
            }
          }

          if (invalidState != null) {
            FleetLogger.debug(
              module = logModuleMapfLow,
              subject = "LowInitPathConflict",
              sr = sr,
              robotName = robotName,
              msg = mapOf(
                "state" to invalidState,
                "constraint" to invalidConstraint,
              ),
            )
            initPathOk = false
            return RobotSolution(
              robotName,
              false,
              "起始状态中违反约束 $invalidState",
              planCost = System.currentTimeMillis() - startOn,
            )
          }

          // 全部合法，赋值给 startStates 供后续使用
          startStates = candidates
        }
      }
    }

    while (goalIndex < goals.size) {
      val goal = goals[goalIndex]
      val one = TargetOneLowResolver(
        sr,
        robotName,
        request,
        areaMapCache,
        collisionModels,
        startStates,
        initStates,
        goal,
        goalIndex,
        goalIndex == goals.size - 1,
        highResolverFriendlyId,
        highNodeId,
        constraints,
        robots,
        cat,
        reserve = goal.reserved,
        o,
      )
      val rs = one.search()
      if (!rs.ok) {
        return rs
      }

      timeNum += rs.timeNum
      solutions += rs

      // 升
      ++goalIndex
      if (goalIndex >= goals.size) continue
      initStates = null
      // 窗口还没到路径终点此时，无需求解预留路径
      // if (o.window!=null && rs.path.last().timeStart > o.window) break

      // 窗口导致最后一个状态可能不是 Goal
      if (rs.path.last().type == StateType.Goal) {
        val last = rs.path.last()

        // TODO 为什么又要检查，为什么不在 TargetOne 里检查
        val startState = rs.path.last().copy(
          type = StateType.Start,
          timeStart = last.timeEnd + 1,
          timeEnd = last.timeEnd + 1,
          timeNum = 0,
        )
        val valid = LowResolverCommon.isValidNeighbor(
          robotName,
          constraints,
          goals[goalIndex],
          null,
          startState,
          o,
          areaMapCache,
        )
        if (valid != null) {
          // 违反约束并不是错误，因为表示不能施加这个错误
          FleetLogger.debug(
            module = logModuleMapfLow,
            subject = "ReserveLowInitPathConflict",
            sr = sr,
            robotName = robotName,
            msg = mapOf("state" to startState, "valid" to valid),
          )
          initPathOk = false
          return RobotSolution(
            robotName,
            false,
            "起始状态中违反约束 $startState",
            planCost = System.currentTimeMillis() - startOn,
          )
        }
        startStates = listOf(startState)
      } else {
        // TODO 注释
        break // 为什么是 break
      }
    } // while

    return mergeSolutions()
  }

  /**
   * TODO 说明
   */
  private fun isGoalsConflict(): Boolean {
    for (c in constraints) {
      // 只检查停靠约束（timeEnd <0）
      if (c.timeEnd >= 0) continue

      for (goal in goals) {
        val state = State(
          0,
          StateType.Goal,
          goal,
          timeStart = 0, // TODO 为什么是 0、1、1
          timeEnd = 1,
          timeNum = 1,
          robotEnterTheta = 0.0,
          robotExitTheta = 0.0,
          moveDirection = MoveDirection.Dual,
          rotateDirection = RotationDirection.EITHER,
          shapes = PolygonsWithBBox(
            RobotLoadCollisionService.buildCollisionShape(
              collisionModels[robotName]!!,
              Pose2D(
                goal.x,
                goal.y,
                0.0, // TODO 这个机器人朝向不对
              ),
              // todo target？货物朝向,预留路径也得考虑货物朝向
            ),
          ),
        )

        if (ResConflictManager.isValidNeighbor(robotName, null, state, constraints, o) != null) return true
      }
    }

    return false
  }

  /**
   * 检查初始路径是否符合约束，initPathOk=false 并返回
   * 如果初始路径中包含前面的目标，则跳过
   *
   * 特殊情况：
   * 1. 所有目标都在当前点，不需要移动
   * 2. 初始路径已经包括所有目标点，不需要再规划
   */
  private fun checkInitPath() {
    val initPath = request.initPath
    if (initPath.isNullOrEmpty()) return

    val firstStartTime = initPath.first().timeStart
    if (firstStartTime != 0L) {
      throw IllegalArgumentException("First init path node time start not 0, but $firstStartTime")
    }

    var path = mutableListOf<State>()

    for (state in initPath) {
      val valid = LowResolverCommon.isValidNeighbor(
        robotName,
        constraints,
        goals[0], // TODO goals[goalIndex]
        null,
        state,
        o,
        areaMapCache,
      )
      if (valid != null) {
        // 违反约束并不是错误，因为表示不能施加这个错误
        FleetLogger.debug(
          module = logModuleMapfLow,
          subject = "LowInitPathConflict",
          sr = sr,
          robotName = robotName,
          msg = mapOf("state" to state, "valid" to valid),
        )
        initPathOk = false
        return
      }

      path += state

      // 某个目标已经得解
      if (state.type == StateType.Goal) {
        val timeNum = path.last().timeEnd - path.first().timeStart + 1
        this.timeNum += timeNum
        solutions += RobotSolution(
          robotName,
          true,
          null,
          path,
          cost = timeNum.toDouble(), // 让它等于时间成本
          // minF = 0.0,
          expandedCount = 0,
          generatedCount = 0,
          timeStart = path.first().timeStart,
          timeEnd = path.last().timeEnd,
          timeNum = timeNum,
        )
        val lastState = path.last()
        goalIndex++
        path = mutableListOf() // 重置下一段路径
        // 下一个目标点规划的起点就是 initPath 的最后一个状态
        // TODO 如果卸货，碰撞模型会变小
        startStates = listOf(
          lastState.copy(
            pathIndex = goalIndex,
            type = StateType.Start,
            timeStart = lastState.timeEnd + 1,
            timeEnd = lastState.timeEnd + 1,
            timeNum = 0,
          ),
        )
      }
    }

    initPathStates = path.ifEmpty { null }
  }

  /**
   * 将多目标的解合并
   */
  private fun mergeSolutions(): RobotSolution {
    var expandedCount = 0L
    var generatedCount = 0L
    var pathIndex = 0
    val path = mutableListOf<State>()

    for ((index, sol) in solutions.withIndex()) {
      // 有多个目标，且当前为最后一个目标时，认为是预留的
      // if(o.window!=null && lastState != null && lastState.timeEnd > o.window) continue
      // val newPath = if(index == goals.size -1 && goals.size >1){
      //   // 只保留最多3个状态
      //   sol.path.take(4).map { it.copy(pathIndex = pathIndex++,reserve = true) }
      // }else{
      //   lastState = sol.path.last()
      //   val path = sol.path.trimToWindow(o.window)
      //   path
      // }

      val newPath = if (goals.size > 1 && index == goals.size - 1) {
        // 接续 pathIndex；标记 reserve（预留路径）
        sol.path.map { it.copy(pathIndex = ++pathIndex, reserve = true) }
      } else {
        pathIndex = sol.path.last().pathIndex
        sol.path
      }

      path += newPath
      // cost += sol.cost
      // minF = sol.minF
      expandedCount += sol.expandedCount
      generatedCount += sol.generatedCount
    }

    // 最后无限停靠
    // val timeStart = path.last().timeStart
    // path[path.size - 1] = path.last().copy(timeEnd = timeStart+50, timeNum = 50L)

    return RobotSolution(
      robotName,
      true,
      null,
      path = path,
      cost = timeNum.toDouble(), // 让它等于时间成本
      // minF = minF,
      expandedCount = expandedCount,
      generatedCount = generatedCount,
      planCost = System.currentTimeMillis() - startOn,
      timeNum = timeNum,
      timeStart = 0,
      timeEnd = timeNum,
    )
  }
}

/**
 * 单目标底层搜索
 */
class TargetOneLowResolver(
  private val sr: SceneRuntime, // TODO 去掉对 sr 的依赖
  val robotName: String,
  private val request: RobotPlanRequest,
  private val areaMapCache: AreaMapCache,
  private val collisionModels: Map<String, RobotLoadCollisionModel>,
  private val startStates: List<State>?, // 可能有多个起点 TODO 大量平局可能
  private val initStates: List<State>?, // 固定开始路径
  private val goal: RoutePosition,
  private val goalIndex: Int,
  private val finalGoal: Boolean,
  val highResolverFriendlyId: String,
  val highNodeId: Long,
  private val constraints: List<RobotConstraint>,
  private val robots: Map<String, RobotPlanContext>, // 所有机器人的，用于计算冲突
  private val cat: ConflictAvoidanceTable,
  private val reserve: Boolean,
  private val o: PathFindingOption,
) {

  private val logModuleMapfLow = "LowRv1"

  val id = IdHelper.oidStr()

  private val startOn = System.currentTimeMillis()

  private var nodeIndex = 0
  private var generatedCount = 0L
  private var expandedCount = 0L

  private var startH: Double? = null

  val nodeLogs = mutableListOf<String>()

  private val logLow = sr.getDevConfigAsBool("logLow") == true

  private val openSet = PriorityQueue<LowNode> { n1, n2 ->
    // 小堆，f 值小的在前面、g 值大的在前面
    if (n1.f != n2.f) {
      n1.f.compareTo(n2.f)
    } else {
      n2.g.compareTo(n1.g)
    }
  }

  private val focalSet = PriorityQueue<LowNode> { n1, n2 ->
    // Sort order (see "Improved Solvers for Bounded-Suboptimal Multi-Agent Path Finding" by Cohen et. al.)
    // 1. lowest focalHeuristic
    // 2. lowest fScore
    // 3. highest gScore
    // 4. prefer wait over move if at same location
    // 小堆，冲突少；g 大；f 小；
    if (n1.focalHeuristic != n2.focalHeuristic) {
      n1.focalHeuristic.compareTo(n2.focalHeuristic)
    } else {
      if (n1.g != n2.g) {
        n2.g.compareTo(n1.g)
      } else if (n1.f != n2.f) {
        n1.f.compareTo(n2.f)
      } else if (n1.state.isSameLocation(n2.state) && n1.state.type != n2.state.type) {
        n1.state.type.order.compareTo(n2.state.type.order)
      } else {
        // 打破平局
        n1.index.compareTo(n2.index)
      }
    }
  }

  // closeSet 移除，使用 node.inOpen=false 代表已关闭
  private val allNodeDict = mutableMapOf<String, LowNode>()

  // === 路径偏离引导常量 ===

  private val devOffTrack = 2.0 // 不在参考最短路
  private val devOnPath = 2.0 // 在最短路但不是期望下一点
  private val devBackTrack = 10.0 // 深度回退或反向一步额外冲突权重
  private val devWait = 0.05 // 原地等待的额外偏离权重
  private val backTrackDepth = 30 // 回退检测深度

  // === 参考无约束最短路 ===
  private var refPath: List<String> = emptyList()
  private var refIndex: Map<String, Int> = emptyMap()

  fun search(): RobotSolution {
    if (logLow) {
      nodeLogs += VenusHelper.logTimestamp() + "底层单目标规划开始 H=$highNodeId | 机器人=$robotName" +
        " | 起始状态=$startStates | 起始路径=$initStates | 目标=$goal | 约束=$constraints"
    }

    var success = false
    try {
      initOrStart()
      val rs = doSearch()
      val lowElapsed = System.currentTimeMillis() - startOn
      if (!rs.ok) {
        FleetLogger.trace(
          module = logModuleMapfLow,
          subject = "LowSearchNoSolution",
          sr = sr,
          robotName = robotName,
          msg = mapOf(
            "highNodeId" to highNodeId,
            "initStates" to initStates,
            "startStates" to startStates,
            "goal" to goal,
            "robotConstraints" to constraints,
          ),
        )
      }
      if (rs.ok) success = true
      FleetLogger.trace(
        module = logModuleMapfLow,
        subject = "expand num",
        sr = sr,
        robotName = robotName,
        msg = mapOf(
          "===expand num===" to expandedCount,
          "elapsedMs" to lowElapsed,
        ),
      )

      if (logLow) nodeLogs += VenusHelper.logTimestamp() + "规划结果: $rs"
      if (logLow) nodeLogs += "扩展节点数=$expandedCount, 耗时=${lowElapsed}ms"

      return rs
    } finally {
      if (logLow) VenusLogService.logLow(this, success, System.currentTimeMillis() - startOn)
    }
  }

  /**
   * 起点有两种情况，有 initStates 和没有，没有走 startStates
   */
  private fun initOrStart() {
    if (initStates != null) {
      var parent: LowNode? = null
      for ((i, state) in initStates.withIndex()) {
        val g = (parent?.g ?: 0.0) + state.timeNum // 不再对 g 增加回退惩罚

        val cost = calculateHeuristic(state)
        val h = cost.roundTwoDigital() // 时间两位小数
        startH = h
        val focalH = focalHeuristic(cat, parent?.state, state).toDouble()
        val f = (g + h).roundTwoDigital()

        val node = LowNode(
          goalIndex,
          nodeIndex++,
          state,
          parent,
          f = f,
          focalHeuristic = focalH,
          g = g,
        )
        parent = node
        // 只把固定开始路径的最后一个节点加入 open
        if (i != initStates.size - 1) {
          node.inOpen = false
        } else {
          node.inOpen = true
          openSet += node
          focalSet += node
          allNodeDict[node.state.stateTimeKey()] = node
        }
      }
    } else {
      for (state in startStates!!) {
        val cost = calculateHeuristic(state)
        val h = cost.roundTwoDigital() // 时间两位小数
        startH = h
        val node = LowNode(
          goalIndex,
          nodeIndex++,
          state,
          null,
          f = h,
          focalHeuristic = 0.0,
          g = 0.0,
        )

        node.inOpen = true
        openSet += node
        focalSet += node
        allNodeDict[node.state.stateTimeKey()] = node
      }
    }

    generatedCount = openSet.size.toLong()
    val rr = sr.mustGetRobot(robotName)
    // 计算无约束最短参考路，用于偏离度评估
    // TODO 起点和终点应该取传给这个 TargetOne 的起点和终点
    if (refPath.isEmpty() && openSet.isNotEmpty()) {
      val startNode = openSet.peek()
      val startPointName = if (!startNode.state.toPosition.pointName.isNullOrEmpty()) {
        startNode.state.toPosition.pointName
      } else {
        MapService.findBestStartPointNameForPlan(rr)
      }
      val goalPointName = goal.pointName
      if (startPointName != null && goalPointName != null) {
        val rr = sr.mustGetRobot(robotName)
        val pts = DijkstraCache.getPathOrCompute(rr, areaMapCache.areaId, startPointName, goalPointName)
        if (!pts.isNullOrEmpty()) {
          refPath = pts
          refIndex = refPath.mapIndexed { idx, pn -> pn to idx }.toMap()
        }
      }
    }
  }

  private fun doSearch(): RobotSolution {
    // todo 要考虑碰撞检测
    earlyFeasibilityCheck()?.let { return it }

    // areaMapCache.pathKeyMap.size * 4 + // 每条路径正走倒走、旋转
    //   areaMapCache.pointNameMap.size * (o.maxWaitingTimesOnState + 1) // 在点位上等待 [0,o.maxWaitingTimesOnState]

    var fMin = openSet.peek()!!.f
    areaMapCache.pathKeyMap.size * 4 + // 每条路径正走倒走、旋转
      areaMapCache.pointNameMap.size * (o.maxWaitingTimesOnState + 1) // 在点位上等待 [0,o.maxWaitingTimesOnState]
    val window = o.window

    // 求解限时
    val lowResolverTimeout = sr.getDevConfigAsLong("lowResolverTimeout") ?: 2000

    while (focalSet.isNotEmpty()) {
      if (lowResolverTimeout > 0 && System.currentTimeMillis() - startOn > lowResolverTimeout) {
        FleetLogger.warn(
          module = logModuleMapfLow,
          subject = "LowSearchTimeout",
          sr = sr,
          robotName = robotName,
          msg = mapOf(
            "elapsedMs" to (System.currentTimeMillis() - startOn),
            "expandedCount" to expandedCount,
          ),
        )
        return RobotSolution(
          robotName,
          false,
          "低层规划超时",
          planCost = System.currentTimeMillis() - startOn,
          expandedCount = expandedCount,
          generatedCount = generatedCount,
        )
      }

      val current: LowNode = focalSet.poll()
      if (!current.inOpen) continue

      // 目前界面上无法配置，所以先写死进行测试
      // TODO 什么无法配置？
      val incressPoint: List<String> = mutableListOf(
        "LM501", "LM1664", "LM610", "LM1663", "LM1668", "LM442", "LM440",
        "LM439", "LM444", "LM435", "LM1670", "LM2410", "LM2408", "LM2412", "LM1671", "LM554", "LM442", "LM1673",
        "LM464", "LM524", "LM616",
      )

      ++expandedCount
      current.inOpen = false

      // 判断到达目标
      // TODO 把这段代码整体封装出去
      if (LowResolverCommon.isGoal(goal, current.state)) {
        FleetLogger.trace(
          module = logModuleMapfLow,
          subject = "Goal",
          sr = sr,
          robotName = robotName,
          msg = mapOf("goalIndex" to goalIndex, "goal" to goal),
        )

        // TODO 封装一下与地图交互，如果有问题，抛异常就行了
        val p = areaMapCache.pointNameMap[current.state.toPosition.pointName]!!.point
        // 增加在目标点工作的状态
        val extraState = VenusHelper.moveToGoalToWorkingGoal(robotName, current.state, o, p)
        val extraNode = LowNode(
          current.goalIndex,
          nodeIndex++,
          extraState,
          current,
          f = current.f,
          g = current.g, // 不变
          focalHeuristic = focalHeuristic(cat, current.state, extraState).toDouble(),
        )

        val path = buildPath(extraNode)
        FleetLogger.debug(
          module = logModuleMapfLow,
          subject = "LowSolutionFound",
          sr = sr,
          robotName = robotName,
          msg = mapOf(
            "highNodeId" to highNodeId,
            "g" to extraNode.g,
            "expandedCount" to expandedCount,
            "path" to path,
          ),
        )

        val timeNum = path.last().timeEnd - path.first().timeStart + 1
        return RobotSolution(
          robotName,
          true,
          cost = timeNum.toDouble(),
          // minF = current.f,
          planCost = System.currentTimeMillis() - startOn,
          expandedCount = expandedCount,
          generatedCount = generatedCount,
          timeNum = timeNum,
          timeStart = path.first().timeStart,
          timeEnd = path.last().timeEnd,
          // fromState = startState,
          // toState = goalState,
          path = path,
        )
      }

      // H 下降了 window 的值
      // TODO 说明文档
      if (window != null &&
        (startH!! - (current.f - current.g)) > window &&
        current.state.timeStart > window &&
        !incressPoint.contains(current.state.toPosition.pointName) &&
        areaMapCache.pointNameMap[current.state.toPosition.pointName]?.point?.linkedPathAsJointResource == false
      ) {
        val path = buildPath(current)
        FleetLogger.debug(
          module = logModuleMapfLow,
          subject = "LowSolutionFoundEarlyReturnByWindow",
          sr = sr,
          robotName = robotName,
          msg = mapOf(
            "highNodeId" to highNodeId,
            "g" to current.g,
            "expandedCount" to expandedCount,
            "path" to path,
            "robotConstraints" to constraints,
          ),
        )

        val timeNum = path.last().timeEnd - path.first().timeStart + 1
        return RobotSolution(
          robotName,
          true,
          cost = timeNum.toDouble(),
          // minF = current.f,
          planCost = System.currentTimeMillis() - startOn,
          expandedCount = expandedCount,
          generatedCount = generatedCount,
          timeNum = timeNum,
          timeStart = path.first().timeStart,
          timeEnd = path.last().timeEnd,
          // fromState = startState,
          // toState = goalState,
          path = path,
        )
      }

      // ------------------------------------
      // 产生子节点
      // ------------------------------------
      val neighbors = getNeighbors(current)
      generatedCount += neighbors.size

      // FleetLogger.debug(
      //   robotName = robotName,
      //   module = logModuleMapfLow,
      //   msg = "产生邻居。从=${current.state}，邻居=$neighbors",
      // )

      for (neighbor in neighbors) {
        val g = current.g + neighbor.timeNum // 不再对 g 增加回退惩罚

        val cost = calculateHeuristic(neighbor)
        val h = cost.roundTwoDigital() // 时间两位小数

        // 计算冲突增量 + 偏离增量
        val conflictDelta = focalHeuristic(cat, current.state, neighbor).toDouble()
        val deviationDelta = computeDeviation(current, neighbor)
        // val deviationDelta = 0
        val newFocalH = current.focalHeuristic + conflictDelta + deviationDelta
        val f = (g + h).roundTwoDigital()

        val newNode = LowNode(
          current.goalIndex,
          nodeIndex++,
          neighbor,
          current,
          f = f,
          focalHeuristic = newFocalH,
          g = g,
        )

        if (logLow) {
          nodeLogs +=
            "有效邻居 $newNode | parent focalHeuristic = ${current.focalHeuristic}| conflictDelta $conflictDelta | deviationDelta $deviationDelta| from=${current.state}"
        }

        addToOpenFocal(newNode, current, fMin)
      }

      val fMinOld = fMin
      fMin = getMinF()

      updateFocalSet(fMinOld, fMin)
    }

    return RobotSolution(
      robotName,
      false,
      "找不到路径",
      planCost = System.currentTimeMillis() - startOn,
      expandedCount = expandedCount,
      generatedCount = generatedCount,
      // fromState = startState,
      // toState = goalState,
    )
  }

  /**
   * TODO 补充说明
   */
  private fun calculateHeuristic(neighbor: State): Double = DijkstraCache.getOrCompute(
    sr.mustGetRobot(robotName),
    if (neighbor.toPosition.pointName != null) {
      neighbor.toPosition.pointName
    } else {
      neighbor.toPosition.pathStartPointName
    }!!,
    goal.pointName!!,
    neighbor.toEndHead!!,
  ) ?: run {
    admissibleHeuristic(neighbor, goal, request).roundTwoDigital()
  }

  /**
   * 获取 open 堆中最小 f 值。本来查看堆顶就行，到哪由于 inOpen 导致需要查几次
   */
  private fun getMinF(): Double {
    if (openSet.isEmpty()) return 0.0

    // 寻找 open 中真在堆顶且（inOpen=true）的节点
    // TODO 小优化，可以先 peek 再 poll
    var top: LowNode?
    // 注意！！！ iterator() 或 for (e in openSet) 只是顺着堆数组下标遍历，不会按优先级顺序
    var node = openSet.poll() ?: return 0.0
    while (!node.inOpen) {
      node = openSet.poll() ?: return 0.0
    }
    top = node
    openSet += node // 再加回去

    return top.f
  }

  /**
   * 因最小 f 值改变更新 focalSet
   */
  private fun updateFocalSet(oldMinF: Double, newMinF: Double) {
    if (newMinF <= oldMinF) return

    for (node in openSet) {
      // 之前不在 focalSet，本轮新加入 focalSet 的节点
      // TODO focalSet 本身能避免重复加入节点吧
      if (node.f > oldMinF * o.lowW && node.f <= newMinF * o.lowW) focalSet += node
    }
  }

  /**
   * 返回的路径开始是 Start，结尾是 Goal
   * 最后一个状态无限等待
   */
  private fun buildPath(lastNode: LowNode): List<State> {
    val lastState = lastNode.state

    val rawPath = mutableListOf(lastState)
    var prevNode = lastNode.parent
    while (prevNode != null) {
      rawPath += prevNode.state
      prevNode = prevNode.parent
    }
    rawPath.reverse()

    FleetLogger.debug(
      module = logModuleMapfLow,
      subject = "RawPath",
      sr = sr,
      robotName = robotName,
      msg = mapOf("rawPath" to rawPath),
    )

    // 找到的路径的开始部分与起始路径相同
    checkInitPathMatching(rawPath)

    // 同时合并在同一位置的多个连续的等待节点。只合并等待节点！初始路径不能修改。
    val path: MutableList<State> = ArrayList(rawPath.size)
    val initPath = initStates
    for ((i, state) in rawPath.withIndex()) {
      if (initPath != null && i < initPath.size) {
        path += state // 初始路径保持不变
      } else {
        if (i == 0) {
          path += state
        } else {
          val prevState = path[path.size - 1]
          // 暂时允许将起点后连续的在起点等待合并为一个，TODO 下发第一个节点在起点等待，不会上报，直接从后续节点 index 上报
          if ((prevState.type == StateType.Wait || prevState.type == StateType.Start) &&
            state.type == StateType.Wait &&
            prevState.isSameLocation(state)
          ) {
            // FleetLogger.debug(
            //   robotName = robotName,
            //   module = logModuleMapfLow,
            //   subject = "SolutionMergeSameState",
            //   "合并同一位置节点：$prevState -> $state",
            // )
            path[path.size - 1] = prevState.copy(
              timeStart = prevState.timeStart,
              timeEnd = state.timeEnd,
              timeNum = prevState.timeNum + state.timeNum,
            )
          } else {
            path += state
          }
        }
      }
    }

    // 二次检查
    var last: State? = null
    for ((si, s) in path.withIndex()) {
      if (s.timeEnd > 0 && s.timeEnd < s.timeStart) throw BzError("errCodeErr", "Path $si time end < time start")
      // if (last != null && si != 0) {
      //   if (last.timeEnd != s.timeStart - 1) throw BzError("errCodeErr", "Path $si time not continuous")
      // }
      last = s
    }

    // 注意不能从 0 开始，要从 initPath 的 index 开始，不然假如 initPath 的同一点位有多个点话
    // 重规划的时候新的路径很难定位此时完成的
    // TODO 为什么
    val firstIndex = path.first().pathIndex
    for ((i, s) in path.withIndex()) {
      s.pathIndex = i + firstIndex
    }

    return path
  }

  /**
   * 检查搜索出来的路径与起始路径匹配
   */
  private fun checkInitPathMatching(path: List<State>) {
    val initPath = initStates ?: return
    for ((i, si) in initPath.withIndex()) {
      if (i >= path.size || path[i] != si) {
        FleetLogger.error(
          module = logModuleMapfLow,
          subject = "SolutionWithoutInitPath",
          sr = sr,
          robotName = robotName,
          msg = mapOf("initPath" to initPath, "path" to path),
        )
        throw BzError("errCode", "找到的路径与初始路径不符")
      }
    }
  }

  /**
   * 在同一个位置等多久了
   * TODO 其实可以增加一个字段记录，不比回溯
   */
  private fun getWaitingTimes(n: LowNode): Long {
    var times = 0L
    var current: LowNode? = n
    var parent = n.parent
    while (current != null && parent != null && current.state.isSameLocation(parent.state)) {
      times += current.state.timeNum
      current = current.parent
      parent = parent.parent
    }
    return times
  }

  /**
   * 产生有效的邻居节点；但不插入集合
   */
  private fun getNeighbors(fromNode: LowNode): List<State> {
    val fromState = fromNode.state

    val neighbors = mutableListOf<State>()

    when (fromState.toPosition.type) {
      RoutePositionType.Point -> {
        // 在点上，遍历前向路径
        val pr = areaMapCache.pointNameMap[fromState.toPosition.pointName] ?: return neighbors
        for (fp in pr.forwardPaths) {
          addNeighborByPath(goalIndex, neighbors, fromState, fp)
        }

        if (getWaitingTimes(fromNode) < o.maxWaitingTimesOnState) {
          // 在点上等待
          // TODO 封装产生等待节点的函数
          addNeighborIfValid(
            neighbors,
            fromState,
            fromState.copy(
              pathIndex = fromState.pathIndex + 1,
              type = StateType.Wait,
              toPosition = fromState.toPosition.copy(
                type = RoutePositionType.Point,
                pointName = if (!fromState.toPosition.pointName.isNullOrEmpty()) {
                  fromState.toPosition.pointName
                } else {
                  // todo 选择 pathStartPointName 还是 pathEndPointName？
                  fromState.toPosition.pathStartPointName
                },
                pathKey = null,
              ),
              robotEnterTheta = fromState.robotExitTheta,
              robotExitTheta = fromState.robotExitTheta,
              loadEnterTheta = fromState.loadExitTheta,
              loadExitTheta = fromState.loadExitTheta,
              timeStart = fromState.timeEnd + 1,
              timeEnd = fromState.timeEnd + o.waitingOnStateMinSec,
              timeNum = o.waitingOnStateMinSec,
              shapes = PolygonsWithBBox(
                RobotLoadCollisionService.buildCollisionShape(
                  collisionModels[robotName]!!,
                  Pose2D(
                    fromState.toPosition.x,
                    fromState.toPosition.y,
                    fromState.toEndHead!!,
                  ),
                  fromState.loadExitTheta,
                ),
              ),
            ),
          )
        }
      }

      // TODO 目前只有最初的起点在路径上；暂时不支持发到路径上；把在路径上也看做在某个虚拟点上？
      RoutePositionType.Path -> {
        // 在路径上
        val pr = areaMapCache.pathKeyMap[fromState.toPosition.pathKey]!!
        // 在路径上，去路径终点
        addNeighborByPath(goalIndex, neighbors, fromState, pr.path)

        // 机器人可能在反向路径上！
        val reversePathKey = MapPath.getKey(pr.path.toPointName, pr.path.fromPointName)
        val reversePathR = areaMapCache.pathKeyMap[reversePathKey]
        if (reversePathR != null) {
          addNeighborByPath(goalIndex, neighbors, fromState, reversePathR.path)
        }

        if (getWaitingTimes(fromNode) < o.maxWaitingTimesOnState) {
          // 在线路上等待
          addNeighborIfValid(
            neighbors,
            fromState,
            fromState.copy(
              pathIndex = fromState.pathIndex + 1,
              type = StateType.Wait,
              timeStart = fromState.timeEnd + 1,
              timeEnd = fromState.timeEnd + o.waitingOnStateMinSec,
              timeNum = o.waitingOnStateMinSec,
            ),
          )
        }
      }
    }

    return neighbors
  }

  /**
   * 尝试从 fromState 按 path 移动
   * 首先要在起点旋转，进入路径；行走；最后在终点旋转至指定弧度
   */
  private fun addNeighborByPath(goalIndex: Int, neighbors: MutableList<State>, fromState: State, path: MapPath) {
    // 起点机器人最终弧度，为什么不直接使用 fromState.robotExitTheta
    val robotStartTheta = fromState.toEndHead!!.normalizeRadian().roundRadianPrecision()

    val pathEnterTheta = path.tracePoints.first().tangent.normalizeRadian().roundRadianPrecision()

    val pathExitTheta = path.tracePoints.last().tangent.normalizeRadian().roundRadianPrecision()

    // 倒走 & 不限制的路径
    if (path.moveDirection != MoveDirection.Forward) {
      val robotEnterTheta = pathEnterTheta.reverseAzimuth() // 反向行驶，机器人进入角度为路径进入角的反向
      val robotExitTheta = pathExitTheta.reverseAzimuth()   // 反向行驶，机器人退出角度为路径退出角的反向

      // 对反向行驶的机器人角度进行窄边修正
      val (finalRobotEnterTheta, finalRobotExitTheta) = processNarrowSideAheadForRobot(
        path, 
        fromState, 
        robotEnterTheta, 
        robotExitTheta, 
        isPathDirectionForward = false // 明确告知是反向行驶
      )

      // 添加反向行驶的邻居状态
      addNeighborByRotation(
        neighbors,
        fromState,
        path,
        MoveDirection.Backward,
        robotStartTheta,
        finalRobotEnterTheta,
        finalRobotExitTheta,
        true
      )
    }
    // 处理【正向 (Forward)】行驶
    if (path.moveDirection != MoveDirection.Backward) {
      val robotEnterTheta = pathEnterTheta // 正向行驶，机器人进入角度为路径进入角
      val robotExitTheta = pathExitTheta   // 正向行驶，机器人退出角度为路径退出角

      // 对正向行驶的机器人角度进行窄边修正
      val (finalRobotEnterTheta, finalRobotExitTheta) = processNarrowSideAheadForRobot(
        path, 
        fromState, 
        robotEnterTheta, 
        robotExitTheta, 
        isPathDirectionForward = true // 明确告知是正向行驶
      )

      // 添加正向行驶的邻居状态
      addNeighborByRotation(
        neighbors,
        fromState,
        path,
        MoveDirection.Forward,
        robotStartTheta,
        finalRobotEnterTheta,
        finalRobotExitTheta,
        true
      )
    }
  }

  /**
   * 【核心修复】实现从货物需求到机器人指令的正确转换
   * 严格分离"货物朝向"和"机器人朝向"的处理逻辑
   * @param path 路径
   * @param fromState 起始状态
   * @param originalRobotEnterTheta 原始的机器人进入角度
   * @param originalRobotExitTheta 原始的机器人退出角度
   * @param isPathDirectionForward 表示当前是按路径的正向（true）还是反向（false）行驶
   * @return 转换后的机器人朝向对（进入角度，退出角度）
   */
  private fun processNarrowSideAheadForRobot(
    path: MapPath,
    fromState: State,
    originalRobotEnterTheta: Double,
    originalRobotExitTheta: Double,
    isPathDirectionForward: Boolean
  ): Pair<Double, Double> {
    // 检查是否需要进行窄边通行处理
    if (!shouldPerformNarrowSideAhead(path, fromState)) {
        return originalRobotEnterTheta to originalRobotExitTheta
    }

    // 检查当前是否在换向点
    val currentPointName = fromState.toPosition.pointName
    val isRotationPoint = currentPointName?.let { pointName ->
      areaMapCache.pointNameMap[pointName]?.point?.containerRotateAllowed == true
    } ?: false

    // 只有在换向点才进行窄边通行调整
    if (!isRotationPoint) {
        return originalRobotEnterTheta to originalRobotExitTheta
    }

    val loadToRobotOffset = getLoadToRobotOffset(robotName)

    // 步骤1：计算原始的货物朝向（基于机器人朝向和固定偏移关系）
    val originalLoadEnterTheta = (originalRobotEnterTheta + loadToRobotOffset).normalizeRadian()
    val originalLoadExitTheta = (originalRobotExitTheta + loadToRobotOffset).normalizeRadian()

    // 步骤2：获取路径的正向切线角度（用于窄边对齐计算）
    val pathEnterTheta = path.tracePoints.first().tangent.normalizeRadian().roundRadianPrecision()
    val pathExitTheta = path.tracePoints.last().tangent.normalizeRadian().roundRadianPrecision()

    // 步骤3：【关键】调用货物朝向调整函数，获取满足窄边通行要求的货物朝向
    val (adjustedLoadEnterTheta, adjustedLoadExitTheta) = adjustLoadThetaForNarrowSideAhead(
        path,
        fromState,
        originalLoadEnterTheta,
        originalLoadExitTheta,
        pathEnterTheta,
        pathExitTheta
    )

    // 步骤4：【核心映射】将调整后的货物朝向转换为机器人应有的朝向
    val baseRobotEnterTheta = calculateRobotThetaFromLoadTheta(adjustedLoadEnterTheta, robotName)
    val baseRobotExitTheta = calculateRobotThetaFromLoadTheta(adjustedLoadExitTheta, robotName)

    // 步骤5：根据实际的行驶方向进行最终调整
    return if (isPathDirectionForward) {
        // 正向行驶：直接使用基于货物朝向计算出的机器人朝向
        baseRobotEnterTheta to baseRobotExitTheta
    } else {
        // 反向行驶：机器人朝向需要取反
        baseRobotEnterTheta.reverseAzimuth() to baseRobotExitTheta.reverseAzimuth()
    }
  }

  /**
   * 指定路径和进入方向、旋转方式
   */
  private fun addNeighborByRotation(
    neighbors: MutableList<State>,
    fromState: State,
    path: MapPath,
    moveDirection: MoveDirection,
    robotStartTheta: Double, // 机器人开始这个状态前，即上一个状态的最终角度
    robotEnterTheta: Double, // 机器人进入路径的起始角度
    robotExitTheta: Double, // 机器人离开路径的最终角度
    minor: Boolean, // 表示小角度旋转（劣弧）
  ) {
    // TODO 检测 robotStartTheta / robotEnterTheta 接近不需要旋转
    val cm = collisionModels[robotName]!!
    val (dir, delta) = GeoHelper.getRotateDirection(robotStartTheta, robotEnterTheta, minor)

    // ---- 计算货物旋转朝向 ----
    val loadEnterTheta: Double?
    val loadExitTheta: Double?

    if (cm.loaded) {
      val loadStartTheta = fromState.loadExitTheta!!

      if (!cm.loadRotatable) {
        // 货物不可独立旋转，与机器人底盘保持固连，同步旋转
        val deltaToEnter = (robotEnterTheta - robotStartTheta).normalizeRadian()
        val deltaToExit = (robotExitTheta - robotStartTheta).normalizeRadian()

        loadEnterTheta = (loadStartTheta + deltaToEnter).normalizeRadian().roundRadianPrecision()
        loadExitTheta = (loadStartTheta + deltaToExit).normalizeRadian().roundRadianPrecision()
      } else {
        // 货物可独立旋转，检查是否在换向点且需要窄边通行
        val currentPointName = fromState.toPosition.pointName
        val isRotationPoint = currentPointName?.let { pointName ->
          areaMapCache.pointNameMap[pointName]?.point?.containerRotateAllowed == true
        } ?: false

        if (isRotationPoint && shouldPerformNarrowSideAhead(path, fromState)) {
          // 在换向点且需要窄边通行，计算窄边通行所需的货物朝向
          val pathEnterTheta = path.tracePoints.first().tangent.normalizeRadian().roundRadianPrecision()
          val pathExitTheta = path.tracePoints.last().tangent.normalizeRadian().roundRadianPrecision()

          val (adjustedLoadEnterTheta, adjustedLoadExitTheta) = adjustLoadThetaForNarrowSideAhead(
            path, fromState, loadStartTheta, loadStartTheta, pathEnterTheta, pathExitTheta
          )

          loadEnterTheta = adjustedLoadEnterTheta.roundRadianPrecision()
          loadExitTheta = adjustedLoadExitTheta.roundRadianPrecision()
        } else {
          // 不在换向点或不需要窄边通行，货物朝向保持不变
          loadEnterTheta = loadStartTheta
          loadExitTheta = loadStartTheta
        }
      }
    } else {
      // 无货物
      loadEnterTheta = null
      loadExitTheta = null
    }

    // 初始旋转时间
    val rotateTime = delta / request.robotInfo.rotateSpeed

    // 估测行走到达时间
    val timeCost = rotateTime + path.actualLength / request.robotInfo.moveSpeed
    var timeNum = ceil(timeCost).toLong()
    if (timeNum < 1) timeNum = 1L // 原地等待

    val toPoint = areaMapCache.pointNameMap[path.toPointName]!!
    val giveAwayPoint = !refIndex.containsKey(path.toPointName)

    if (giveAwayPoint) {
      val isAp = path.toPointName.contains("AP")

      val shouldSkip = if (isAp) {
        // AP 点：需同时满足 apGiveWayAllowed 以及点自身允许让路
        !o.apGiveWayAllowed || toPoint.point.giveWayNotAllowed
      } else {
        // 普通点只受自身约束
        toPoint.point.giveWayNotAllowed
      }

      if (shouldSkip) return
    }

    val newState = State(
      fromState.pathIndex + 1,
      type = StateType.Move,
      toPosition = RoutePosition(
        type = RoutePositionType.Point,
        pointName = path.toPointName,
        x = toPoint.point.x,
        y = toPoint.point.y,
      ),
      toEndHead = robotExitTheta, // 对于过程中的点默认为退出路径时的角度，不调整
      byPathKey = path.key,
      robotEnterTheta = robotEnterTheta,
      robotExitTheta = robotExitTheta,
      loadEnterTheta = loadEnterTheta,
      loadExitTheta = loadExitTheta,
      moveDirection = moveDirection,
      rotateDirection = if (dir > 0) RotationDirection.COUNTERCLOCKWISE else RotationDirection.CLOCKWISE,
      timeStart = fromState.timeEnd + 1,
      timeEnd = fromState.timeEnd + timeNum,
      timeNum = timeNum,
      shapes = PolygonsWithBBox(
        ResConflictManager.buildEdgeSpace(
          cm,
          fromX = fromState.toPosition.x,
          fromY = fromState.toPosition.y,
          robotStartTheta = robotStartTheta,
          robotEnterTheta = robotEnterTheta,
          loadStartTheta = fromState.loadExitTheta,
          path = path,
          minor = minor,
          moveDirection = moveDirection,
        ),
      ),
    )

    addNeighborIfValid(neighbors, fromState, newState)
  }

  /**
   * 计算偏离参考最短路的增量权重。
   * 同时若检测到深度回退（最近 BACKTRACK_DEPTH 内曾到过同点），
   * 再追加 [DEV_BACKTRACK]。
   */
  private fun computeDeviation(current: LowNode, neighbor: State): Double {
    // 仅 Move 节点考虑深度回退
    var dev = 0.0

    val neighborPoint = neighbor.toPosition.pointName
    val currPoint = current.state.toPosition.pointName

    val idxNeighbor = neighborPoint?.let { refIndex[it] }
    val idxCurrent = currPoint?.let { refIndex[it] }

    dev += when {
      idxNeighbor == null -> devOffTrack
      // 同一 ref 点连续等待或顺着最短路前进都不计偏离
      idxCurrent != null && (idxNeighbor == idxCurrent || idxNeighbor == idxCurrent + 1) -> 0.0
      idxCurrent != null && idxNeighbor == idxCurrent - 1 -> devBackTrack // 向后一步视作回退
      else -> devOnPath
    }

    if (isDeepBacktrack(current, neighbor)) dev += devBackTrack

    if (neighbor.type == StateType.Wait) dev += devWait

    return dev
  }

  /**
   * 检测最近 BACKTRACK_DEPTH 步是否出现回退到同名点位，且两点均位于参考最短路径上
   */
  private fun isDeepBacktrack(current: LowNode, neighbor: State): Boolean {
    if (neighbor.type != StateType.Move) return false

    val neighborPoint = neighbor.toPosition.pointName ?: return false
    val currentPoint = current.state.toPosition.pointName ?: return false

    // 若当前点或目标点不在参考最短路上，则说明可能是避让场景，忽略回退判定
    if (neighborPoint !in refIndex || currentPoint !in refIndex) return false

    // 从 current 往回追溯，寻找最近一次到达同名点位 neighborPoint。
    // 若在追溯过程中出现任何点位不在最短路上，则回退判定失败。
    var node: LowNode? = current
    var steps = 0
    while (node != null && steps < backTrackDepth) {
      val pn = node.state.toPosition.pointName
      if (pn == neighborPoint) return true // 找到早期同名点，且中间未出现 off-path
      if (pn == null || pn !in refIndex) return false // 途中出现 off-path
      node = node.parent
      steps++
    }
    return false
  }

  /**
   * 计算容器的窄边方向偏移角度
   * 窄边通行要求容器的窄边（短边）指向路径方向
   * @param containerType 容器类型
   * @return 窄边相对于容器原始方向（长边方向）的偏移角度（弧度）
   */
  private fun calculateContainerNarrowSideOffset(containerType: SceneContainerType): Double {
    return when {
      // 长宽相等：正方体，不区别窄边或者宽边，使用0度偏移
      kotlin.math.abs(containerType.outerLength - containerType.outerWidth) < 0.01 -> 0.0
      // 长>宽：窄边方向为垂直于长度的方向，需要旋转90°使窄边朝前
      containerType.outerLength > containerType.outerWidth -> PI / 2
      // 宽>长：窄边就是长度方向，不需要旋转，0°
      else -> 0.0
    }
  }

  /**
   * 寻找最佳换向点
   * @param fromPointName 起始点名称
   * @param toPointName 目标点名称
   * @return 换向点名称，如果没有找到返回null
   */
  private fun findBestRotationPoint(fromPointName: String, toPointName: String): String? {
    // 首先尝试向前查找换向点（优先选择距离目标较近的点）
    val forwardRotationPoint = findRotationPointForward(fromPointName, toPointName)
    if (forwardRotationPoint != null) {
      return forwardRotationPoint
    }

    // 如果向前找不到，向后查找
    return findRotationPointBackward(fromPointName, toPointName)
  }

  /**
   * 检查当前状态是否需要在换向点进行容器旋转
   * @param fromState 当前状态
   * @param path 要进入的路径
   * @return 如果需要在换向点旋转返回true
   */
  private fun shouldRotateAtRotationPoint(fromState: State, path: MapPath): Boolean {
    // 检查是否需要窄边通行
    if (!shouldPerformNarrowSideAhead(path, fromState)) return false

    // 检查当前是否在换向点
    val currentPointName = fromState.toPosition.pointName ?: return false
    val pointRecord = areaMapCache.pointNameMap[currentPointName] ?: return false

    return pointRecord.point.containerRotateAllowed
  }

  /**
   * 向前查找换向点（优先选择距离目标较近的点）
   */
  private fun findRotationPointForward(fromPointName: String, toPointName: String): String? {
    val rr = sr.mustGetRobot(robotName)
    val shortestPath = DijkstraCache.getPathOrCompute(rr, areaMapCache.areaId, fromPointName, toPointName)

    if (shortestPath.isNullOrEmpty()) return null

    // 从起点向目标点方向查找换向点，优先选择距离目标较近的点
    for (pointName in shortestPath) {
      val pointRecord = areaMapCache.pointNameMap[pointName]
      if (pointRecord != null && pointRecord.point.containerRotateAllowed) {
        return pointName
      }
    }

    return null
  }

  /**
   * 向后查找换向点
   */
  private fun findRotationPointBackward(fromPointName: String, toPointName: String): String? {
    val rr = sr.mustGetRobot(robotName)
    val shortestPath = DijkstraCache.getPathOrCompute(rr, areaMapCache.areaId, fromPointName, toPointName)

    if (shortestPath.isNullOrEmpty()) return null

    // 从目标点向起点方向查找换向点
    for (pointName in shortestPath.reversed()) {
      val pointRecord = areaMapCache.pointNameMap[pointName]
      if (pointRecord != null && pointRecord.point.containerRotateAllowed) {
        return pointName
      }
    }

    return null
  }

  /**
   * 计算窄边通行所需的容器目标朝向
   * @param pathDirection 路径方向（弧度）
   * @param containerNarrowSideOffset 容器窄边偏移角度
   * @return 容器目标朝向（弧度）
   */
  private fun calculateNarrowSideTargetTheta(pathDirection: Double, containerNarrowSideOffset: Double): Double {
    return (pathDirection + containerNarrowSideOffset).normalizeRadian()
  }

  /**
   * 计算从当前朝向到目标朝向的最小角度差
   * @param currentTheta 当前朝向
   * @param targetTheta 目标朝向
   * @return 角度差，如果超过半圆则选择反向的较小角度
   */
  private fun calculateOptimalRotationAngle(currentTheta: Double, targetTheta: Double): Double {
    var angleDiff = (targetTheta - currentTheta).normalizeRadian()
    
    // 如果角度差超过半圆（π），选择反向旋转
    if (kotlin.math.abs(angleDiff) > PI) {
      angleDiff = if (angleDiff > 0) {
        angleDiff - 2 * PI
      } else {
        angleDiff + 2 * PI
      }
    }
    
    return angleDiff
  }

  /**
   * 检查是否需要进行窄边通行处理
   * @param path 路径
   * @param fromState 起始状态
   * @return 如果需要窄边通行返回true
   */
  private fun shouldPerformNarrowSideAhead(path: MapPath, fromState: State): Boolean {
    // 1. 检查路径是否设置了窄边通行
    if (!path.containerShortSideAhead) return false

    // 2. 检查机器人是否有容器
    val cm = collisionModels[robotName] ?: return false
    if (!cm.loaded) return false

    // 3. 检查容器是否支持旋转（机器人必须支持料架在机器人身上旋转）
    val rr = sr.mustGetRobot(robotName)
    val containerType = rr.sr.containerTypes.values.find { it.name == cm.containerTypeName }
    if (containerType == null) return false

    // 4. 检查机器人组是否支持容器旋转（salverNotRotate为false表示支持旋转）
    val group = rr.mustGetGroup()
    if (group.salverNotRotate) return false

    // 5. 检查容器是否可独立旋转（loadRotatable为true表示货物能旋转）
    if (!cm.loadRotatable) return false

    return true
  }

  /**
   * 【核心修复】专门处理货物窄边通行的朝向调整
   * 此函数只关注货物朝向，不涉及机器人朝向的计算
   * @param path 路径
   * @param fromState 起始状态
   * @param originalLoadEnterTheta 原始货物进入角度
   * @param originalLoadExitTheta 原始货物退出角度
   * @param pathEnterTheta 路径正向进入角度（用于窄边对齐计算）
   * @param pathExitTheta 路径正向退出角度（用于窄边对齐计算）
   * @return 调整后的货物朝向对（进入角度，退出角度）
   */
  private fun adjustLoadThetaForNarrowSideAhead(
    path: MapPath,
    fromState: State,
    originalLoadEnterTheta: Double,
    originalLoadExitTheta: Double,
    pathEnterTheta: Double, // 路径正向进入角度
    pathExitTheta: Double   // 路径正向退出角度
  ): Pair<Double, Double> {
    // 获取容器类型信息
    val rr = sr.mustGetRobot(robotName)
    val cm = collisionModels[robotName]!!
    val containerType = rr.sr.containerTypes.values.find { it.name == cm.containerTypeName }
      ?: return originalLoadEnterTheta to originalLoadExitTheta // 安全回退

    // 计算容器窄边偏移角度
    val narrowSideOffset = calculateContainerNarrowSideOffset(containerType)

    // 定义计算目标货物朝向的核心逻辑
    fun calculateOptimalLoadTheta(currentLoadTheta: Double, pathTheta: Double): Double {
        // 计算窄边通行所需的目标朝向：路径方向 + 窄边偏移
        val targetTheta = (pathTheta + narrowSideOffset).normalizeRadian()

        // 计算当前朝向到目标朝向的角度差
        val angleDiff = calculateOptimalRotationAngle(currentLoadTheta, targetTheta)

        // 如果角度差很小（小于5度），认为已经满足窄边要求，不需要调整
        val tolerance = Math.toRadians(5.0)
        if (kotlin.math.abs(angleDiff) < tolerance) {
            return currentLoadTheta
        }

        // 否则返回目标朝向
        return targetTheta
    }

    // 分别计算进入和退出时的最优货物朝向
    val adjustedLoadEnterTheta = calculateOptimalLoadTheta(originalLoadEnterTheta, pathEnterTheta)
    val adjustedLoadExitTheta = calculateOptimalLoadTheta(originalLoadExitTheta, pathExitTheta)

    return adjustedLoadEnterTheta to adjustedLoadExitTheta
  }

  /**
   * 获取机器人与货物之间的固定偏移角度
   * @param robotName 机器人名称
   * @return 货物相对于机器人底盘的偏移角度（弧度）
   */
  private fun getLoadToRobotOffset(robotName: String): Double {
    val cm = collisionModels[robotName] ?: return 0.0
    return if (cm.loaded) {
      // 使用配置中的货物初始角度作为偏移角
      cm.loadInitTheta
    } else {
      0.0
    }
  }

  /**
   * 将货物朝向转换为机器人朝向
   * 这是修复窄边通行问题的核心映射函数
   * @param desiredLoadTheta 任务需求的货物朝向
   * @param robotName 机器人名称
   * @return 机器人底盘应有的朝向
   */
  private fun calculateRobotThetaFromLoadTheta(desiredLoadTheta: Double, robotName: String): Double {
    // 从配置中获取该机器人型号的货物偏移角
    val loadToRobotOffset = getLoadToRobotOffset(robotName)
    // 计算机器人应有的朝向：货物朝向减去偏移角
    return (desiredLoadTheta - loadToRobotOffset).normalizeRadian()
  }

  private fun addNeighborIfValid(neighbors: MutableList<State>, fromState: State, toState: State) {
    val constraint = LowResolverCommon.isValidNeighbor(
      robotName,
      constraints,
      goal,
      fromState,
      toState,
      o,
      areaMapCache,
    )
    if (constraint != null) {
      if (logLow) nodeLogs += "无效邻居 | 约束=$constraint | to=$toState | from=$fromState"
      return
    }
    // 还是加原来的
    neighbors += toState
  }

  /**
   * 添加到 open/focal 集合，按需
   */
  private fun addToOpenFocal(node: LowNode, current: LowNode, fMin: Double) {
    val key = node.state.stateTimeKey()

    val oldNode = allNodeDict[key]
    if (oldNode != null) {
      // 是老节点，然后判断是否在 open 中
      if (oldNode.inOpen) {
        // 在 open 中 f 值降低或冲突数降低
        if (oldNode.f > node.f || oldNode.f == node.f && oldNode.focalHeuristic > node.focalHeuristic) {
          if (node.f <= fMin * o.lowW) { // 需要加入 focal
            if (oldNode.f > fMin * o.lowW) { // 原先是否在 focal 中
              focalSet += node
            } else {
              // 原先在 focal 中
              focalSet.remove(oldNode)
              focalSet += node
            }
          }
          oldNode.inOpen = false
          node.inOpen = true
          openSet += node
          allNodeDict[key] = node
        }
      } else if (oldNode.f > node.f || oldNode.f == node.f && oldNode.focalHeuristic > node.focalHeuristic) {
        // 如果在 close 中
        node.inOpen = true
        allNodeDict[key] = node
        openSet += node
        if (node.f <= fMin * o.lowW) {
          focalSet += node
        }
      }
    } else {
      // 新节点
      node.inOpen = true
      allNodeDict[key] = node
      openSet += node
      if (node.f <= fMin * o.lowW) {
        focalSet += node
      }
    }
  }

  /**
   * 对当前起点到目标做一次快速可达性检查。
   * 主要针对一个空闲车停留在那的情况
   * 如果在锁定点位约束下找不到任何路径，直接返回失败结果，避免进入主搜索循环浪费时间。
   * @return 若不可达则返回失败的 RobotSolution；可达返回 null 继续搜索
   */
  private fun earlyFeasibilityCheck(): RobotSolution? {
    val current = focalSet.first() // TODO peek()??

    val lockedPoints: Set<String> =
      constraints.filter { it.type == ResConstraintType.Spatial && it.timeEnd == -1L } // TODO 只考虑 Spatial 不考虑 Point 吗？
        .mapNotNull { it.pointName }
        .toSet()
    if (lockedPoints.isEmpty()) return null // 无锁定点可直接继续
    val rr = sr.mustGetRobot(robotName)
    // TODO 这个也是在求最短路，真的省时间吗？只做空间搜索所有省时间吗？
    val sp =
      MapService.getShortestPathOfCrossAreas(rr, current.state.toPosition.pointName!!, goal.pointName!!, lockedPoints)
    if (sp.found) return null // 可达继续搜索
    // 不可达：记录日志并返回失败结果
    FleetLogger.warn(
      module = logModuleMapfLow,
      subject = "EarlyNoPath",
      sr = sr,
      robotName = robotName,
      msg = mapOf("lockedPointNames" to lockedPoints),
    )

    return RobotSolution(
      robotName,
      ok = false,
      reason = "初始可达性检查失败，无法找到路径，锁定点：$lockedPoints",
      planCost = System.currentTimeMillis() - startOn,
      expandedCount = 0,
      generatedCount = 0,
    )
  }

  companion object {

    fun admissibleHeuristic(from: State, goal: RoutePosition, request: RobotPlanRequest): Double =
      from.toPosition.euclideanDistance(goal) / request.robotInfo.moveSpeed

    fun focalHeuristic(cat: ConflictAvoidanceTable, fromState: State?, toState: State): Int {
      val currName = fromState?.toPosition?.pointName ?: toState.toPosition.pointName
      val nextName = toState.toPosition.pointName
      if (currName == null || nextName == null) return 0
      for (t in toState.timeStart..toState.timeEnd) {
        if (cat.hasConflict(currName, nextName, t)) return 1
      }
      return 0
    }
  }
}

object LowResolverCommon {

  /**
   * 如果是目标点，还要判断是否能持续站得住
   */
  fun isValidNeighbor(
    robotName: String,
    robotConstraints: List<RobotConstraint>,
    goal: RoutePosition,
    fromState: State?,
    toState: State,
    o: PathFindingOption,
    areaMapCache: AreaMapCache,
  ): RobotConstraint? {
    val valid = ResConflictManager.isValidNeighbor(
      robotName,
      fromState,
      toState,
      robotConstraints,
      o,
    )

    if (valid != null) return valid

    // 如果是目标点，增加动作或停靠时间
    if (isGoal(goal, toState)) {
      val p = areaMapCache.pointNameMap[toState.toPosition.pointName]!!.point
      val extraState = VenusHelper.moveToGoalToWorkingGoal(robotName, toState, o, p)
      val valid2 = ResConflictManager.isValidNeighbor(robotName, null, extraState, robotConstraints, o)
      if (valid2 != null) return valid2
    }

    return null
  }

  // 考虑多目标
  fun isGoal(goal: RoutePosition, state: State): Boolean = state.isSameLocation(goal)
}